#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown to PDF Converter using ReportLab
"""

import os
import sys
import argparse
import markdown
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re
from html import unescape


class MarkdownToPDFConverter:
    """Markdown转PDF转换器"""
    
    def __init__(self):
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """设置中文字体"""
        self.chinese_font = 'Helvetica'
        try:
            font_paths = [
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/Helvetica.ttc',
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        self.chinese_font = 'ChineseFont'
                        break
                    except:
                        continue
        except:
            pass
    
    def setup_styles(self):
        """设置样式"""
        self.styles = getSampleStyleSheet()
        
        self.styles.add(ParagraphStyle(
            name='ChineseTitle',
            parent=self.styles['Title'],
            fontName=self.chinese_font,
            fontSize=24,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        self.styles.add(ParagraphStyle(
            name='ChineseHeading1',
            parent=self.styles['Heading1'],
            fontName=self.chinese_font,
            fontSize=18,
            spaceAfter=12,
            textColor=colors.darkred
        ))
        
        self.styles.add(ParagraphStyle(
            name='ChineseHeading2',
            parent=self.styles['Heading2'],
            fontName=self.chinese_font,
            fontSize=16,
            spaceAfter=10,
            textColor=colors.darkorange
        ))
        
        self.styles.add(ParagraphStyle(
            name='ChineseNormal',
            parent=self.styles['Normal'],
            fontName=self.chinese_font,
            fontSize=12,
            spaceAfter=6,
            leading=18
        ))
    
    def parse_markdown_to_elements(self, md_content: str):
        """解析Markdown内容为PDF元素"""
        md = markdown.Markdown(extensions=[
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.toc'
        ])
        html_content = md.convert(md_content)
        
        elements = []
        lines = html_content.split('\n')
        current_text = ""
        
        for line in lines:
            line = line.strip()
            if not line:
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                continue
            
            if line.startswith('<h1>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseTitle']))
                elements.append(Spacer(1, 12))
                
            elif line.startswith('<h2>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseHeading1']))
                elements.append(Spacer(1, 8))
                
            elif line.startswith('<h3>'):
                if current_text:
                    elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
                    current_text = ""
                title = re.sub(r'<[^>]+>', '', line)
                title = unescape(title)
                elements.append(Paragraph(title, self.styles['ChineseHeading2']))
                elements.append(Spacer(1, 6))
                
            elif line.startswith('<p>'):
                text = re.sub(r'<[^>]+>', '', line)
                text = unescape(text)
                if text:
                    current_text += text + " "
                    
            elif not line.startswith('<'):
                text = unescape(line)
                if text:
                    current_text += text + " "
        
        if current_text:
            elements.append(Paragraph(current_text, self.styles['ChineseNormal']))
        
        return elements
    
    def convert(self, input_file: str, output_file: str = None) -> str:
        """转换Markdown文件为PDF"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        if not output_file:
            input_path = Path(input_file)
            output_file = str(input_path.with_suffix('.pdf'))
        
        print(f"开始转换: {input_file} -> {output_file}")
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            elements = self.parse_markdown_to_elements(md_content)
            
            doc = SimpleDocTemplate(
                output_file,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            doc.build(elements)
            
            print(f"转换完成: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"转换失败: {str(e)}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Markdown to PDF Converter')
    parser.add_argument('input', help='输入的Markdown文件路径')
    parser.add_argument('-o', '--output', help='输出的PDF文件路径')
    
    args = parser.parse_args()
    
    try:
        converter = MarkdownToPDFConverter()
        output_file = converter.convert(args.input, args.output)
        print(f"✅ 转换成功! PDF文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
